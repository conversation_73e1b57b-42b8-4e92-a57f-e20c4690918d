import { styled, t } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import SubMenu from 'src/features/home/<USER>';
import { useEffect, useMemo, useRef, useState } from 'react';
import { isEqual } from 'lodash';
import FolderCard from '../../components/FolderCard';
import { Entity, FolderListDashboard } from '../../components/types';
import FolderDashboardList from '../../components/FolderDashboardList';

const StyledContainer = styled.div`
  padding-inline: ${({ theme }) => theme.gridUnit * 4}px;
  padding-bottom: ${({ theme }) => theme.gridUnit * 4}px;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: ${({ theme }) => theme.gridUnit * 4}px;
  flex: 1;

  .folder-dashboard-list-view {
    margin: 0 !important;

    .header {
      padding: ${({ theme }) => theme.gridUnit * 4}px;
      background-color: ${({ theme }) => theme.colors.grayscale.light5};
    };
    }
  }
`;

const mock: Entity[] = [
  {
    id: 1,
    title_ru: 'Глобальная папка 1',
    title_en: 'Global folder 1',
    description_ru: 'Описание глобальной папки 1',
    description_en: 'Description of global folder 1',
    parent: 0,
    type: 'folder',
    children: [
      {
        id: 11,
        title_ru: 'Аналитика',
        title_en: 'Analytics',
        description_ru: 'Папка с аналитическими отчётами',
        description_en: 'Folder with analytics reports',
        parent: 1,
        type: 'folder',
        children: [
          {
            id: 111,
            title_ru: 'Продажи Q1',
            title_en: 'Sales Q1',
            parent: 11,
            type: 'dashboard',
            is_certified: true,
          },
          {
            id: 112,
            title_ru: 'Маркетинг',
            title_en: 'Marketing',
            parent: 11,
            type: 'dashboard',
            is_certified: false,
          },
        ],
      },
      {
        id: 12,
        title_ru: 'Финансы',
        title_en: 'Finance',
        description_ru: 'Бюджеты и отчётность',
        description_en: 'Budgets and reporting',
        parent: 1,
        type: 'folder',
        children: [
          {
            id: 121,
            title_ru: 'Бюджет 2024',
            title_en: 'Budget 2024',
            parent: 12,
            type: 'dashboard',
            is_certified: false,
          },
        ],
      },
      {
        id: 13,
        title_ru: 'HR и подбор',
        title_en: 'HR & Recruitment',
        description_ru: 'Отчёты по найму и вовлечённости',
        description_en: 'Reports on hiring and engagement',
        parent: 1,
        type: 'folder',
        children: [
          {
            id: 131,
            title_ru: 'Вакансии',
            title_en: 'Jobs',
            parent: 13,
            type: 'dashboard',
            is_certified: false,
          },
        ],
      },
    ],
  },
];

const FolderPage = () => {
  const [folderData, setFolderData] = useState<Entity[] | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedFolderId, setSelectedFolderId] = useState<number | null>(null);
  const initialfolderData = useRef<Entity[] | null>(null);

  const hasChanges = useMemo(
    () => !isEqual(initialfolderData.current, folderData),
    [folderData],
  );

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setFolderData(mock);
      initialfolderData.current = mock;
      setLoading(false);
    }, 1000);
  }, []);

  const addEntitiesToData = (
    currentData: Entity[],
    newEntities: Entity[],
  ): Entity[] =>
    currentData.map(entity => {
      if (entity.id === selectedFolderId && entity.type === 'folder') {
        return {
          ...entity,
          children: [...(entity.children || []), ...newEntities],
        };
      }
      if (entity.type === 'folder' && entity.children) {
        return {
          ...entity,
          children: addEntitiesToData(entity.children, newEntities),
        };
      }
      return entity;
    });

  const addDashboardsToFolder = (dashboards: FolderListDashboard[]) => {
    const dashboardEntities: Entity[] = dashboards.map(dashboard => ({
      id: dashboard.id,
      title_ru: dashboard.dashboard_title_ru,
      title_en: dashboard.dashboard_title,
      parent: selectedFolderId || 0,
      type: 'dashboard',
      is_certified: Boolean(
        dashboard.certified_by && dashboard.certification_details,
      ),
    }));
    setFolderData(prevData => {
      if (!prevData) return null;
      return addEntitiesToData(prevData, dashboardEntities);
    });
  };

  return (
    <>
      <SubMenu name={t('Folder')} />
      <StyledContainer>
        <FolderCard
          title={t('Global')}
          browse="/folder/0"
          editMode
          hasChanges={hasChanges}
          loading={loading}
          folderData={folderData}
          setFolderData={setFolderData}
          setSelectedFolderId={setSelectedFolderId}
        />
        <FolderDashboardList addDashboardsToFolder={addDashboardsToFolder} />
      </StyledContainer>
    </>
  );
};

export default FolderPage;
