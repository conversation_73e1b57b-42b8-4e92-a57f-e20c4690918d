import { Entity, Folder, isFolderType, TreeNodeData } from './types';

export const buildTreeData = (
  entities: Entity[],
  locale: string,
): TreeNodeData[] =>
  entities.map(entity => ({
    key: `${entity.type}-${entity.id}`,
    title:
      locale === 'ru'
        ? entity.title_ru || entity.title_en
        : entity.title_en || entity.title_ru,
    entity,
    id: entity.id,
    type: entity.type,
    selectable: entity.type === 'folder',
    isLeaf: entity.type === 'dashboard',
    children: isFolderType(entity)
      ? buildTreeData(entity.children || [], locale)
      : [],
  }));

export const filterTreeData = (
  data: TreeNodeData[],
  searchTerm: string,
): TreeNodeData[] => {
  if (!searchTerm) return data;

  const matchesSearch = (node: TreeNodeData) =>
    node.entity.title_ru.toLowerCase().includes(searchTerm.toLowerCase()) ||
    node.entity.title_en.toLowerCase().includes(searchTerm.toLowerCase());

  const filterNode = (node: TreeNodeData): TreeNodeData | null => {
    if (isFolderType(node.entity) && matchesSearch(node)) {
      return {
        ...node,
      };
    }

    if (isFolderType(node.entity)) {
      const filteredChildren = node.children
        ?.map(filterNode)
        .filter(Boolean) as TreeNodeData[];

      if (filteredChildren.length > 0) {
        return {
          ...node,
          children: filteredChildren,
        };
      }
      return null;
    }

    if (matchesSearch(node)) {
      return node;
    }

    return null;
  };

  return data.map(filterNode).filter(Boolean) as TreeNodeData[];
};

export const removeEntityFromData = (
  entities: Entity[],
  entityId: number,
): Entity[] =>
  entities
    .filter(entity => entity.id !== entityId)
    .map(entity => {
      if (entity.type === 'folder' && entity.children) {
        return {
          ...entity,
          children: removeEntityFromData(entity.children, entityId),
        };
      }
      return entity;
    });

export const updateFolderInData = (
  entities: Entity[],
  updatedFolder: Folder,
): Entity[] =>
  entities.map(entity => {
    if (entity.id === updatedFolder.id && entity.type === 'folder') {
      return updatedFolder;
    }
    if (entity.type === 'folder' && entity.children) {
      return {
        ...entity,
        children: updateFolderInData(entity.children, updatedFolder),
      };
    }
    return entity;
  });

export const addFolderToData = (
  entities: Entity[],
  folder: Folder,
): Entity[] => {
  if (folder.parent === 0) {
    return [...entities, folder];
  }

  return entities.map(entity => {
    if (entity.id === folder.parent && entity.type === 'folder') {
      return {
        ...entity,
        children: [...(entity.children || []), folder],
      };
    }
    if (entity.type === 'folder' && entity.children) {
      return {
        ...entity,
        children: addFolderToData(entity.children, folder),
      };
    }
    return entity;
  });
};
