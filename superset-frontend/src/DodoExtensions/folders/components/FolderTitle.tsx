import { bootstrapData } from 'src/preamble';
import { css, t } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import InfoTooltip from 'src/components/InfoTooltip';
import { Folder } from './types';

const locale = bootstrapData?.common?.locale || 'en';

const FolderTitle = ({
  editMode,
  onEdit,
  folder,
}: {
  editMode: boolean;
  onEdit?: (folder: Folder) => void;
  folder: Folder;
}) => {
  const title =
    locale === 'ru'
      ? folder.title_ru || folder.title_en
      : folder.title_en || folder.title_ru;

  const description =
    locale === 'ru'
      ? folder.description_ru || folder.description_en
      : folder.description_en || folder.description_ru;

  return (
    <div className="item-title">
      <span
        css={css`
          display: flex;
          align-items: center;
          gap: 2px;
        `}
      >
        {title}
        {description && (
          <InfoTooltip
            tooltip={description}
            placement="top"
            iconStyle={{ fontSize: '20px' }}
          />
        )}
      </span>
      {editMode && (
        <span
          role="button"
          tabIndex={0}
          title={t('Edit folder')}
          aria-label={t('Edit folder')}
          onClick={e => {
            e.stopPropagation();
            if (onEdit) {
              onEdit(folder);
            }
          }}
        >
          <Icons.EditOutlined iconSize="m" />
        </span>
      )}
    </div>
  );
};

export default FolderTitle;
