export interface Folder {
  id: number;
  type: 'folder';
  title_ru: string;
  title_en: string;
  description_ru: string;
  description_en: string;
  parent: number;
  children: Entity[];
}

export interface Dashboard {
  id: number;
  type: 'dashboard';
  title_ru: string;
  title_en: string;
  is_certified: boolean;
  parent: number;
}

export type Entity = Folder | Dashboard;

export interface FoldersData {
  global: Entity[];
  personal: Entity[];
  team?: Entity[];
}

export interface TreeNodeData {
  key: string;
  entity: Entity;
  id: number;
  type: string;
  children?: TreeNodeData[];
}

export interface FolderListDashboard {
  id: number;
  url: string;
  dashboard_title: string;
  dashboard_title_ru: string;
  certified_by: string;
  certification_details: string;
}

export const isFolderType = (entity: unknown): entity is Folder =>
  entity !== null &&
  typeof entity === 'object' &&
  'type' in entity &&
  typeof (entity as { type: unknown }).type === 'string' &&
  (entity as { type: string }).type === 'folder';
