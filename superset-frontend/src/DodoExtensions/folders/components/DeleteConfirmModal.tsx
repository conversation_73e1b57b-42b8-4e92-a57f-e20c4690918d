import Modal from 'src/components/Modal';
import { styled, t } from '@superset-ui/core';
import { Dashboard } from './types';

const ModalContent = styled.div`
  padding: 16px 0;
`;

const TitleText = styled.p`
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
`;

const WarningText = styled.p`
  margin-bottom: 0;
  color: ${({ theme }) => theme.colors.error.base};
`;

interface DeleteConfirmModalProps {
  show: boolean;
  dashboard: Dashboard | null;
  onConfirm: () => void;
  onCancel: () => void;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  show,
  dashboard,
  onConfirm,
  onCancel,
}) => {
  if (!dashboard) return null;

  const dashboardTitle = dashboard.title_ru || dashboard.title_en;

  return (
    <Modal
      title={t('Delete Dashboard')}
      show={show}
      onHide={onCancel}
      onHandledPrimaryAction={onConfirm}
      primaryButtonName={t('Delete')}
      primaryButtonType="danger"
      width="500px"
    >
      <ModalContent>
        <TitleText>
          {t('Are you sure you want to delete %s?', dashboardTitle)}
        </TitleText>
        <WarningText>
          {t(
            'This dashboard will be permanently deleted. This action cannot be undone.',
          )}
        </WarningText>
      </ModalContent>
    </Modal>
  );
};

export default DeleteConfirmModal;
