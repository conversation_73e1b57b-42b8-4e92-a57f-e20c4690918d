import { useEffect, useMemo, useRef } from 'react';
import { Form } from 'antd-v5';
import { TreeSelect } from 'antd';
import { styled, t } from '@superset-ui/core';
import { Input, TextArea } from 'src/components/Input';
import Modal from 'src/components/Modal';
import Button from 'src/components/Button';
import { Entity, Folder } from './types';

const StyledForm = styled(Form)`
  .form-item {
    margin-bottom: ${({ theme }) => theme.gridUnit * 4}px;
  }

  .language-section {
    margin-bottom: ${({ theme }) => theme.gridUnit * 6}px;

    h4 {
      margin-bottom: ${({ theme }) => theme.gridUnit * 3}px;
      color: ${({ theme }) => theme.colors.grayscale.dark1};
      font-weight: 600;
    }
  }

  .parent-folder-section {
    margin-bottom: ${({ theme }) => theme.gridUnit * 6}px;

    .ant-tree-select {
      width: 100%;
    }
  }
`;

const buildTreeSelectData = (entities: Entity[]): any[] =>
  entities
    .filter(entity => entity.type === 'folder')
    .map(entity => ({
      title: entity.title_ru || entity.title_en,
      value: entity.id,
      key: entity.id,
      children:
        entity.type === 'folder' && entity.children
          ? buildTreeSelectData(entity.children)
          : [],
    }));

interface FolderModalProps {
  show: boolean;
  folder?: Folder | null;
  parentFolder?: Folder | null;
  availableFolders?: Entity[];
  mode: 'add' | 'edit';
  onExit: () => void;
  onSuccess: (folder: Folder) => void;
  onDelete?: () => void;
}

interface FormValues {
  title_ru: string;
  title_en: string;
  description_ru?: string;
  description_en?: string;
  parent_id?: number;
}

const FolderModal: React.FC<FolderModalProps> = ({
  show,
  folder,
  parentFolder,
  availableFolders = [],
  mode,
  onExit,
  onSuccess,
  onDelete,
}) => {
  const initialValuesRef = useRef<FormValues | null>(null);
  const [form] = Form.useForm<FormValues>();
  const currentValues = Form.useWatch([], form);
  const isAddMode = mode === 'add';

  useEffect(() => {
    if (!show) return;

    let values: FormValues;
    if (isAddMode) {
      values = {
        parent_id: parentFolder?.id || undefined,
        title_ru: '',
        title_en: '',
        description_ru: '',
        description_en: '',
      };
    } else if (folder) {
      values = {
        title_ru: folder.title_ru || '',
        title_en: folder.title_en || '',
        description_ru: folder.description_ru || '',
        description_en: folder.description_en || '',
      };
    } else {
      return;
    }

    form.setFieldsValue(values);
    initialValuesRef.current = values;
  }, [show, isAddMode, folder, parentFolder, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      let resultFolder: Folder;

      if (mode === 'add') {
        resultFolder = {
          id: Date.now(),
          type: 'folder',
          title_ru: values.title_ru,
          title_en: values.title_en,
          description_ru: values.description_ru || '',
          description_en: values.description_en || '',
          parent: values.parent_id ? values.parent_id : 0,
          children: [],
        };
      } else {
        resultFolder = {
          ...folder!,
          title_ru: values.title_ru,
          title_en: values.title_en,
          description_ru: values.description_ru || '',
          description_en: values.description_en || '',
        };
      }

      onSuccess(resultFolder);
      onExit();
    } catch (error) {
      console.error('Error validating form:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onExit();
  };

  const isSubmitDisabled = () => {
    const initialValues = initialValuesRef.current;
    if (!initialValues) return true;

    if (isAddMode) {
      return (
        !currentValues.title_ru ||
        !currentValues.title_en ||
        currentValues.parent_id === undefined
      );
    }

    return (
      currentValues.title_ru === initialValues.title_ru &&
      currentValues.title_en === initialValues.title_en &&
      currentValues.description_ru === initialValues.description_ru &&
      currentValues.description_en === initialValues.description_en
    );
  };

  const treeSelectData = useMemo(() => {
    if (!isAddMode) {
      return [];
    }

    return [
      {
        title: t('Root Level'),
        value: null,
        key: 'root',
      },
      ...buildTreeSelectData(availableFolders),
    ];
  }, [availableFolders, isAddMode]);

  const footer = (
    <>
      <Button onClick={handleCancel}>{t('Cancel')}</Button>
      {!isAddMode && (
        <Button buttonStyle="danger" onClick={onDelete}>
          {t('Delete')}
        </Button>
      )}
      <Button
        buttonStyle="primary"
        onClick={handleSubmit}
        disabled={isSubmitDisabled()}
      >
        {isAddMode ? t('Create') : t('Save')}
      </Button>
    </>
  );

  return (
    <Modal
      title={isAddMode ? t('Add New Folder') : t('Edit Folder')}
      show={show}
      onHide={handleCancel}
      onHandledPrimaryAction={handleSubmit}
      footer={footer}
      width="600px"
      responsive
    >
      <StyledForm form={form} layout="vertical" requiredMark={false}>
        {isAddMode && (
          <div className="parent-folder-section">
            <Form.Item
              name="parent_id"
              label={t('Parent Folder')}
              className="form-item"
            >
              <TreeSelect
                placeholder={t('Select parent folder')}
                treeData={treeSelectData}
                treeDefaultExpandAll
                allowClear
              />
            </Form.Item>
          </div>
        )}

        <div className="language-section">
          <h4>{t('Russian')}</h4>
          <Form.Item
            name="title_ru"
            label={t('Title')}
            className="form-item"
            rules={[
              {
                required: true,
                message: t('Please enter folder title in Russian'),
              },
              {
                max: 500,
                message: t('Name must be less than 500 characters'),
              },
            ]}
          >
            <Input placeholder={t('Enter folder title in Russian')} />
          </Form.Item>

          <Form.Item
            name="description_ru"
            label={t('Description')}
            className="form-item"
          >
            <TextArea
              rows={3}
              placeholder={t('Enter folder description in Russian')}
            />
          </Form.Item>
        </div>

        <div className="language-section">
          <h4>{t('English')}</h4>
          <Form.Item
            name="title_en"
            label={t('Title')}
            className="form-item"
            rules={[
              {
                required: true,
                message: t('Please enter folder title in English'),
              },
              {
                max: 500,
                message: t('Name must be less than 500 characters'),
              },
            ]}
          >
            <Input placeholder={t('Enter folder title in English')} />
          </Form.Item>

          <Form.Item
            name="description_en"
            label={t('Description')}
            className="form-item"
          >
            <TextArea
              rows={3}
              placeholder={t('Enter folder description in English')}
            />
          </Form.Item>
        </div>
      </StyledForm>
    </Modal>
  );
};

export default FolderModal;
