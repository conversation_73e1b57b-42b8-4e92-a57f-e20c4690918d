import { useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { Tree, TreeNodeProps } from 'antd-v5';
import { styled, t } from '@superset-ui/core';
import { bootstrapData } from 'src/preamble';
import Card from 'src/components/Card';
import Icons from 'src/components/Icons';
import { Input } from 'src/components/Input';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import Loading from 'src/components/Loading';
import { Entity, isFolderType, TreeNodeData, Folder, Dashboard } from './types';
import {
  addFolderToData,
  buildTreeData,
  filterTreeData,
  removeEntityFromData,
  updateFolderInData,
} from './utils';
import FolderModal from './FolderModal';
import FolderTitle from './FolderTitle';
import DeleteConfirmModal from './DeleteConfirmModal';
import { useDragAndDrop } from '../hooks/useDragAndDrop';

const locale = bootstrapData?.common?.locale || 'en';

const StyledCard = styled(Card)`
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colors.grayscale.light5};

  .header-title {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit}px;

    svg {
      color: ${({ theme }) => theme.colors.primary.base};
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit * 2}px;
    color: ${({ theme }) => theme.colors.primary.base};
  }

  .browse-link {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit}px;
    line-height: 1rem;
    color: ${({ theme }) => theme.colors.primary.base} !important;

    a {
      color: ${({ theme }) => theme.colors.primary.base} !important;
    }

    span {
      height: 14px;
    }
  }

  .antd5-card-body {
    display: flex;
    flex-direction: column;
    padding: ${({ theme }) => theme.gridUnit * 6}px;
    flex: 1;
  }

  .tree-container {
    margin-top: ${({ theme }) => theme.gridUnit * 5}px;
    overflow-y: auto;
    max-height: 100%;
    flex: 1;
  }

  .tree {
    background-color: transparent;
  }

  .antd5-tree-treenode,
  .antd5-tree-title {
    width: 100%;
  }

  .antd5-tree-node-content-wrapper,
  antd5-tree-node-content-wrapper-normal {
    display: flex;
    flex: 1;
  }

  .antd5-tree-node-content-wrapper:has(.dashboard) {
    &:hover {
      background-color: transparent;
    }
  }

  .search-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    color: ${({ theme }) => theme.colors.grayscale.base};
  }

  .item-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit * 2}px;
    flex: 1;

    span[role='button'] {
      line-height: 14px;
    }

    .dash-delete-icon {
      margin-right: ${({ theme }) => theme.gridUnit * 5}px;
      color: ${({ theme }) => theme.colors.error.base};
    }
  }

  .certified-icon {
    color: ${({ theme }) => theme.colors.primary.base};
  }

  .antd5-tree-treenode.dragging {
    opacity: 0.5;
  }

  .antd5-tree-node-content-wrapper:hover {
    background-color: ${({ theme }) => theme.colors.grayscale.light4};
  }
`;

const DashboardTitle = ({
  editMode,
  data,
  onDelete,
}: {
  editMode: boolean;
  data: Dashboard;
  onDelete?: (dashboard: Dashboard) => void;
}) =>
  editMode ? (
    <div className="item-title dashboard">
      <span>
        {locale === 'ru'
          ? data.title_ru || data.title_en
          : data.title_en || data.title_ru}
      </span>
      {onDelete && (
        <span
          role="button"
          tabIndex={0}
          className="dash-delete-icon"
          onClick={e => {
            e.stopPropagation();
            onDelete(data);
          }}
          aria-label={t('Delete dashboard')}
        >
          <Icons.Trash iconSize="m" />
        </span>
      )}
    </div>
  ) : (
    <Link to={`/superset/dashboard/${data.id}`} style={{ color: 'inherit' }}>
      {data.is_certified && (
        <Icons.Certified className="certified-icon" iconSize="m" />
      )}
      {locale === 'ru'
        ? data.title_ru || data.title_en
        : data.title_en || data.title_ru}
    </Link>
  );

const ActionButtons = ({
  onAddFolder,
  onSave,
  hasChanges,
}: {
  onAddFolder?: () => void;
  onSave?: () => void;
  hasChanges?: boolean;
}) => (
  <div className="action-buttons">
    <span
      role="button"
      aria-label={t('Add folder')}
      tabIndex={0}
      onClick={onAddFolder}
    >
      <Icons.FolderAddOutlined iconSize="xl" />
    </span>
    <span
      role="button"
      aria-label={t('Save')}
      tabIndex={0}
      onClick={onSave}
      style={{
        opacity: hasChanges ? 1 : 0.5,
        cursor: hasChanges ? 'pointer' : 'default',
      }}
    >
      <Icons.SaveOutlined iconSize="xl" />
    </span>
  </div>
);

const BrowseLink = ({ to }: { to: string | undefined }) =>
  to ? (
    <Link to={to} className="browse-link">
      <span>{t('Browse dashboards')}</span>
      <Icons.ArrowRightOutlined iconSize="m" />
    </Link>
  ) : (
    <></>
  );

interface IProps {
  editMode: boolean;
  canEdit?: boolean;
  title: string;
  folderData: Entity[] | null;
  setFolderData?: (data: Entity[] | null) => void;
  browse?: string;
  loading: boolean;
  hasChanges?: boolean;
  setSelectedFolderId?: (id: number | null) => void;
}

const FolderCard = ({
  title,
  browse,
  canEdit,
  folderData,
  setFolderData,
  editMode,
  hasChanges,
  loading,
  setSelectedFolderId,
}: IProps) => {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [addFolderModalVisible, setAddFolderModalVisible] = useState(false);
  const [folderToEdit, setFolderToEdit] = useState<Folder | null>(null);
  const [dashboardToDelete, setDashboardToDelete] = useState<Dashboard | null>(
    null,
  );
  const { addSuccessToast, addDangerToast } = useToasts();

  const { onDrop, allowDrop } = useDragAndDrop(
    folderData || [],
    setFolderData!,
    addDangerToast,
  );

  const stopSearching = () => {
    setSearchTerm('');
    searchInputRef.current!.blur();
  };

  const handleDeleteFolder = (folder: Folder) => {
    setFolderData?.(removeEntityFromData(folderData || [], folder.id));
    setFolderToEdit(null);
  };

  const handleConfirmDelete = () => {
    if (!dashboardToDelete) return;

    setFolderData?.(
      removeEntityFromData(folderData || [], dashboardToDelete.id),
    );
    setDashboardToDelete(null);
    addSuccessToast(t('Dashboard will be deleted when you click Save'));
  };

  const handleEditFolder = (updatedFolder: Folder) => {
    setFolderData?.(updateFolderInData(folderData || [], updatedFolder));
    addSuccessToast(t('Changes will be saved when you click Save'));
  };

  const handleAddFolder = (newFolder: Folder) => {
    // Add new folder to data
    setFolderData?.(addFolderToData(folderData || [], newFolder));
    addSuccessToast(
      t('Folder is added. Changes will be saved when you click Save'),
    );
  };

  const handleSave = () => {
    // Here you would implement the actual API calls to save all changes
    addSuccessToast(t('All changes saved successfully'));
  };

  const titleNode = canEdit ? (
    <span className="header-title">
      <Link to="/folder/0">
        <Icons.EditOutlined iconSize="l" />
      </Link>
      <span>{title}</span>
    </span>
  ) : (
    title
  );

  if (loading) {
    return (
      <StyledCard title={titleNode}>
        <Loading position="inline-centered" />
      </StyledCard>
    );
  }

  const extra = editMode ? (
    <ActionButtons
      onAddFolder={() => setAddFolderModalVisible(true)}
      onSave={handleSave}
      hasChanges={hasChanges}
    />
  ) : (
    <BrowseLink to={browse} />
  );

  const treeData = buildTreeData(folderData || [], locale);
  const filteredTreeData = filterTreeData(treeData, searchTerm);

  return (
    <StyledCard title={titleNode} extra={extra}>
      <Input
        type="text"
        ref={searchInputRef as any}
        value={searchTerm}
        placeholder={t('Search')}
        onChange={event => setSearchTerm(event.target.value)}
        prefix={
          <div className="search-icon">
            <Icons.Search iconSize="m" />
          </div>
        }
        suffix={
          <div className="search-icon">
            {searchTerm && (
              <Icons.XLarge iconSize="m" onClick={stopSearching} />
            )}
          </div>
        }
      />
      {folderData && (
        <>
          <div className="tree-container">
            <Tree
              treeData={filteredTreeData}
              titleRender={nodeData => {
                const { entity } = nodeData as TreeNodeData;

                const titleContent = isFolderType(entity) ? (
                  <FolderTitle
                    editMode={editMode}
                    folder={entity}
                    onEdit={() => setFolderToEdit(entity)}
                  />
                ) : (
                  <DashboardTitle
                    editMode={editMode}
                    data={entity}
                    onDelete={editMode ? setDashboardToDelete : undefined}
                  />
                );
                return titleContent;
              }}
              switcherIcon={<Icons.CaretDownOutlined />}
              icon={(props: TreeNodeProps & TreeNodeData) => {
                if (props.data.entity.type === 'dashboard') {
                  return <Icons.FundViewOutlined iconSize="m" />;
                }
                return props.expanded ? (
                  <Icons.FolderOpenOutlined iconSize="m" />
                ) : (
                  <Icons.FolderOutlined iconSize="m" />
                );
              }}
              draggable={editMode}
              onDrop={onDrop}
              allowDrop={allowDrop}
              className="tree"
              showLine={!editMode}
              showIcon
              onSelect={
                setSelectedFolderId
                  ? (_, info) => {
                      // @ts-ignore
                      setSelectedFolderId(info.selected ? info.node.id : null);
                    }
                  : undefined
              }
              defaultExpandAll
            />
          </div>

          {/* Modals */}
          <FolderModal
            mode="edit"
            show={Boolean(folderToEdit)}
            folder={folderToEdit}
            onExit={() => setFolderToEdit(null)}
            onSuccess={handleEditFolder}
            onDelete={() => handleDeleteFolder(folderToEdit!)}
          />

          <FolderModal
            mode="add"
            show={addFolderModalVisible}
            parentFolder={null}
            availableFolders={folderData}
            onExit={() => setAddFolderModalVisible(false)}
            onSuccess={handleAddFolder}
          />

          <DeleteConfirmModal
            show={Boolean(dashboardToDelete)}
            dashboard={dashboardToDelete}
            onConfirm={handleConfirmDelete}
            onCancel={() => setDashboardToDelete(null)}
          />
        </>
      )}
    </StyledCard>
  );
};

export default FolderCard;
