import { useCallback } from 'react';
import { TreeProps } from 'antd-v5';
import { t } from '@superset-ui/core';
import { Entity, TreeNodeData } from '../components/types';

const insertAfter = (
  children: Entity[],
  entity: Entity,
  afterSiblingKey: string | null,
): Entity[] => {
  if (!afterSiblingKey) {
    return [...children, entity];
  }
  const [siblingType, siblingId] = afterSiblingKey.split('-');
  const index = children.findIndex(
    child => child.type === siblingType && child.id === Number(siblingId),
  );
  // Вставляем после найденного элемента
  return [
    ...children.slice(0, index + 1),
    entity,
    ...children.slice(index + 1),
  ];
};

export const useDragAndDrop = (
  data: Entity[],
  onDataChange: (newData: Entity[]) => void,
  onError: (message: string) => void,
) => {
  const removeEntityByKey = useCallback(
    (entities: Entity[], key: string): Entity[] => {
      const [entityType, entityId] = key.split('-');

      return entities
        .filter(
          entity =>
            !(entity.type === entityType && entity.id === Number(entityId)),
        )
        .map(entity => {
          if (entity.type === 'folder' && entity.children) {
            return {
              ...entity,
              children: removeEntityByKey(entity.children, key),
            };
          }
          return entity;
        });
    },
    [],
  );

  const addEntityToParent = useCallback(
    (
      entities: Entity[],
      entity: Entity,
      parentId: number | null,
      afterSiblingKey: string | null,
    ): Entity[] => {
      if (parentId === null || parentId === 0) {
        // Add to root level
        return [...entities, entity];
      }

      return entities.map(item => {
        if (item.id === parentId && item.type === 'folder') {
          return {
            ...item,
            children: insertAfter(item.children || [], entity, afterSiblingKey),
          };
        }
        if (item.type === 'folder' && item.children) {
          return {
            ...item,
            children: addEntityToParent(
              item.children,
              entity,
              parentId,
              afterSiblingKey,
            ),
          };
        }
        return item;
      });
    },
    [],
  );

  const onDrop: TreeProps['onDrop'] = useCallback(
    async (info: any) => {
      const { dragNode, node, dropToGap } = info;

      try {
        let targetFolderId: number | null = null; // Determine target folder
        let afterSiblingKey: string | null = null; // Determine position

        if (!dropToGap) {
          // Dropped on a node

          if (node.type === 'dashboard') return; // Can't drop on a dashboard, ignore

          targetFolderId = node.id;
          afterSiblingKey = node.key;
        } else {
          // Dropped between nodes

          const { entity, key } = node;
          targetFolderId = entity.parent;
          afterSiblingKey = key;
        }

        // Update local state
        const { entity: draggedEntity, key: draggedKey } = dragNode;
        if (draggedEntity) {
          // Remove from current position
          let newData = removeEntityByKey(data, draggedKey);

          // Update parent reference
          const updatedEntity = {
            ...draggedEntity,
            parent: targetFolderId || 0,
          };

          // Add to new position
          newData = addEntityToParent(
            newData,
            updatedEntity,
            targetFolderId,
            afterSiblingKey,
          );

          onDataChange(newData);
        }
      } catch (error) {
        onError(t('Failed to move item'));
      }
    },
    [data, removeEntityByKey, addEntityToParent, onDataChange, onError],
  );

  const allowDrop: TreeProps['allowDrop'] = useCallback(
    ({ dragNode, dropNode, dropPosition }) => {
      const dragNodeData = dragNode as TreeNodeData;
      const dropNodeData = dropNode as TreeNodeData;

      // Don't allow dropping on dashboards
      if (dropNodeData.type === 'dashboard' && dropPosition === 0) {
        return false;
      }

      // Don't allow dropping a folder into itself or its descendants
      if (dragNodeData.type === 'folder' && dropNodeData.type === 'folder') {
        const isDescendant = (parentKey: string, childKey: string): boolean =>
          childKey.startsWith(`${parentKey}-`);

        if (
          dropNodeData.key === dragNodeData.key ||
          isDescendant(dragNodeData.key, dropNodeData.key)
        ) {
          return false;
        }
      }

      return true;
    },
    [],
  );

  return {
    onDrop,
    allowDrop,
  };
};
